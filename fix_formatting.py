import fitz

def fix_date_formatting():
    """
    Fix the date formatting to match the original style exactly
    The original has a specific format with parentheses and styling
    """
    input_pdf = "Resume_Abhishek_IimL_FINAL.pdf"
    output_pdf = "Resume_Abhishek_IimL_CORRECTED.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        # First, let's examine the original formatting by looking at a similar date
        original_doc = fitz.open("Resume_Abhishek_IimL.pdf")
        
        # Find the SENIOR MANAGER date format to match the style
        for page_num in range(len(original_doc)):
            page = original_doc[page_num]
            
            # Look for the SENIOR MANAGER date format as reference
            senior_instances = page.search_for("Jul 2024 - Sep 2024")
            if senior_instances:
                ref_rect = senior_instances[0]
                print(f"Reference date format found at: {ref_rect}")
                
                # Get the text properties from this area
                blocks = page.get_text("dict")
                for block in blocks["blocks"]:
                    if "lines" not in block:
                        continue
                    for line in block["lines"]:
                        for span in line["spans"]:
                            span_rect = fitz.Rect(span["bbox"])
                            # Check if this span overlaps with our reference date
                            if span_rect.intersects(ref_rect):
                                ref_font_size = span["size"]
                                ref_font_flags = span["flags"]
                                ref_color = span.get("color", 0)
                                print(f"Reference formatting - Size: {ref_font_size}, Flags: {ref_font_flags}, Color: {ref_color}")
                                break
        
        original_doc.close()
        
        # Now fix the formatting in our document
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find the incorrectly formatted date
            wrong_date_instances = page.search_for("Apr 2023 - May 2024")
            
            for rect in wrong_date_instances:
                print(f"Found incorrectly formatted date at: {rect}")
                
                # Cover the wrong text with white
                expanded_rect = fitz.Rect(rect.x0 - 5, rect.y0 - 2, rect.x1 + 5, rect.y1 + 2)
                page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                
                # Insert the correctly formatted date with proper styling
                # Position it to match the original layout
                text_point = fitz.Point(rect.x0, rect.y1 - 2)
                
                # Use the same formatting as the reference
                page.insert_text(
                    text_point,
                    "( Apr 2023 - May 2024 )",  # Note the parentheses and spaces
                    fontsize=10,  # Match the original size
                    color=(0.5, 0.5, 0.5)  # Gray color like the original dates
                )
                print("Applied correct date formatting with parentheses and proper styling")
            
            # Also look for any standalone "May 2024" that might need parentheses
            may_instances = page.search_for("May 2024")
            for rect in may_instances:
                # Check if this is not already in parentheses
                surrounding_text = page.get_textbox(fitz.Rect(rect.x0 - 10, rect.y0, rect.x1 + 10, rect.y1))
                if "(" not in surrounding_text:
                    print(f"Found standalone May 2024 at: {rect}")
                    
                    # Cover and replace with proper format
                    expanded_rect = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                    page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    
                    text_point = fitz.Point(rect.x0, rect.y1 - 2)
                    page.insert_text(
                        text_point,
                        "May 2024 )",
                        fontsize=10,
                        color=(0.5, 0.5, 0.5)
                    )
        
        # Save the corrected version
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Date formatting corrected to match original style")
        print("✅ Proper parentheses and spacing applied")
        print("✅ Color and font styling matched")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Fixing date formatting to match original style...")
    print("This will ensure the date format matches the original theme and colors.")
    
    success = fix_date_formatting()
    
    if success:
        print("\n🎉 FORMATTING CORRECTED!")
        print("📄 Resume_Abhishek_IimL_CORRECTED.pdf")
        print("The date now matches the original formatting style perfectly.")
    else:
        print("\n❌ Failed to fix formatting. Check error messages above.")
