import fitz  # PyMuPD<PERSON>

def create_clean_resume():
    """
    Create a clean resume by copying the original page and redacting the unwanted section
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_CLEAN.pdf"
    
    try:
        # Open the original PDF
        doc = fitz.open(input_pdf)
        
        print("Analyzing PDF content...")
        
        # Find the CATEGORY MANAGER section coordinates
        category_rects = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Search for CATEGORY MANAGER text
            text_instances = page.search_for("CATEGORY MANAGER")
            if text_instances:
                print(f"Found CATEGORY MANAGER on page {page_num + 1}")
                
                # Get the full text to understand the layout
                blocks = page.get_text("dict")
                
                # Find blocks containing the unwanted content
                for block in blocks["blocks"]:
                    if "lines" not in block:
                        continue
                    
                    block_text = ""
                    for line in block["lines"]:
                        for span in line["spans"]:
                            block_text += span["text"] + " "
                    
                    # Check if this block contains CATEGORY MANAGER content
                    if any(phrase in block_text for phrase in [
                        "CATEGORY MANAGER",
                        "Jan 2024 - May 2024", 
                        "Contlo technology pvt ltd",
                        "Built 0 to 1 categories",
                        "Streamlined operations for the team",
                        "Pivotal in defining KPIs",
                        "Hired, onboarded, and trained manpower"
                    ]):
                        # Get the bounding box of this block
                        bbox = fitz.Rect(block["bbox"])
                        category_rects.append((page_num, bbox))
                        print(f"Found content block to remove: {bbox}")
        
        # Create output document by copying and redacting
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Copy the page
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            # Copy the entire page content first
            new_page.show_pdf_page(new_page.rect, doc, page_num)
            
            # Now redact (white out) the unwanted sections
            for rect_page, rect in category_rects:
                if rect_page == page_num:
                    # Draw a white rectangle over the unwanted content
                    new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                    print(f"Redacted area: {rect}")
        
        # Save the result
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"✅ Created: {output_pdf}")
        
        # Verify the result
        verify_doc = fitz.open(output_pdf)
        full_text = ""
        for page in verify_doc:
            full_text += page.get_text()
        verify_doc.close()
        
        if "CATEGORY MANAGER" in full_text:
            print("⚠️  CATEGORY MANAGER text may still be visible")
        else:
            print("✅ CATEGORY MANAGER text removed")
            
        if "Contlo technology" in full_text:
            print("⚠️  Contlo technology text may still be visible")
        else:
            print("✅ Contlo technology text removed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def simple_copy_without_section():
    """
    Alternative: Copy everything except the problematic section
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_SIMPLE.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        # Extract text and remove the section
        full_text = ""
        for page in doc:
            full_text += page.get_text()
        
        # Remove the CATEGORY MANAGER section
        lines = full_text.split('\n')
        cleaned_lines = []
        skip_mode = False
        
        for line in lines:
            line = line.strip()
            
            # Start skipping when we hit CATEGORY MANAGER
            if "CATEGORY MANAGER" in line:
                skip_mode = True
                continue
            
            # Stop skipping when we hit the next major section
            if skip_mode and any(section in line for section in [
                "ASSOCIATE BUSINESS MANAGER",
                "MANAGER - SALES & OPERATIONS", 
                "MANAGEMENT INTERN",
                "SOFTWARE ENGINEER"
            ]):
                skip_mode = False
                cleaned_lines.append(line)
                continue
            
            # Skip lines that are clearly part of the CATEGORY MANAGER section
            if skip_mode or any(phrase in line for phrase in [
                "Contlo technology pvt ltd",
                "Built 0 to 1 categories",
                "Streamlined operations for the team",
                "Pivotal in defining KPIs", 
                "Hired, onboarded, and trained manpower"
            ]):
                continue
            
            cleaned_lines.append(line)
        
        # Create new PDF with cleaned text
        new_doc = fitz.open()
        page = new_doc.new_page()
        
        cleaned_text = '\n'.join(cleaned_lines)
        page.insert_text((50, 50), cleaned_text, fontsize=10)
        
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"✅ Created simple version: {output_pdf}")
        return True
        
    except Exception as e:
        print(f"❌ Error in simple method: {e}")
        return False

if __name__ == "__main__":
    print("Attempting to create clean resume...")
    
    # Try the redaction method first
    success = create_clean_resume()
    
    if not success:
        print("\nTrying simple text-based method...")
        simple_copy_without_section()
    
    print("\nDone! Check the output files.")
