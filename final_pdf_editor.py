import fitz  # PyMuPDF
import re

def create_polished_resume():
    """
    Create a polished PDF resume by removing the CATEGORY MANAGER section
    and maintaining professional formatting
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_polished.pdf"
    
    try:
        # Open the original PDF
        doc = fitz.open(input_pdf)
        
        # Extract text from all pages
        full_text = ""
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            full_text += text
        
        doc.close()
        
        print("Original text extracted successfully")
        
        # Remove the CATEGORY MANAGER section
        # Since ASSOCIATE BUSINESS MANAGER appears before CATEGORY MANAGER,
        # we need to find the next section after CATEGORY MANAGER
        category_start = full_text.find("CATEGORY MANAGER")

        if category_start != -1:
            # Look for the next major section after CATEGORY MANAGER
            # This could be MANAGER - SALES & OPERATIONS or another section
            remaining_text = full_text[category_start:]

            # Find the next section that starts with a job title pattern
            next_section_patterns = [
                "MANAGER - SALES & OPERATIONS",
                "MANAGEMENT INTERN",
                "SOFTWARE ENGINEER",
                "ASSOCIATE BUSINESS MANAGER"
            ]

            next_section_start = -1
            for pattern in next_section_patterns:
                pos = remaining_text.find(pattern)
                if pos > 100:  # Make sure it's not the same section
                    next_section_start = category_start + pos
                    break

            if next_section_start != -1:
                # Remove from CATEGORY MANAGER to the next section
                before_section = full_text[:category_start]
                after_section = full_text[next_section_start:]
                cleaned_text = before_section + after_section

                # Clean up extra whitespace
                cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
                cleaned_text = cleaned_text.strip()

                print("✅ CATEGORY MANAGER section removed successfully")
                print(f"Removed {len(full_text) - len(cleaned_text)} characters")
            else:
                print("❌ Could not find the next section after CATEGORY MANAGER")
                cleaned_text = full_text
        else:
            print("❌ Could not find CATEGORY MANAGER section")
            cleaned_text = full_text
        
        # Create a new PDF with professional formatting
        new_doc = fitz.open()
        
        # Page settings
        page_width = 595  # A4 width in points
        page_height = 842  # A4 height in points
        margin = 50
        
        page = new_doc.new_page(width=page_width, height=page_height)
        
        # Define fonts and styles
        title_font = "helv"
        heading_font = "helv"
        body_font = "helv"
        
        # Split text into lines and format
        lines = cleaned_text.split('\n')
        y_position = margin + 20
        line_height = 14
        
        for line in lines:
            line = line.strip()
            if not line:
                y_position += line_height * 0.5  # Half line for empty lines
                continue
            
            # Check if we need a new page
            if y_position > page_height - margin:
                page = new_doc.new_page(width=page_width, height=page_height)
                y_position = margin + 20
            
            # Determine formatting based on content
            if line.isupper() and len(line) < 50:
                # Main title or section headers
                font_size = 16 if "ABHISHEK KUMAR" in line else 12
                font_flags = fitz.TEXT_ALIGN_CENTER if "ABHISHEK KUMAR" in line else 0
                font_weight = fitz.TEXT_FONT_BOLD
                
                # Center the title
                if "ABHISHEK KUMAR" in line:
                    text_width = fitz.get_text_length(line, fontname=title_font, fontsize=font_size)
                    x_position = (page_width - text_width) / 2
                else:
                    x_position = margin
                
                page.insert_text(
                    (x_position, y_position),
                    line,
                    fontname=title_font,
                    fontsize=font_size,
                    color=(0, 0, 0)
                )
                y_position += line_height * 1.5
                
            elif line.startswith('•') or line.startswith('-'):
                # Bullet points
                page.insert_text(
                    (margin + 20, y_position),
                    line,
                    fontname=body_font,
                    fontsize=10,
                    color=(0, 0, 0)
                )
                y_position += line_height
                
            elif any(char.isdigit() for char in line) and ('(' in line and ')' in line):
                # Job titles with dates
                page.insert_text(
                    (margin, y_position),
                    line,
                    fontname=heading_font,
                    fontsize=11,
                    color=(0, 0, 0)
                )
                y_position += line_height * 1.2
                
            else:
                # Regular text
                # Handle long lines by wrapping
                words = line.split()
                current_line = ""
                
                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    # Approximate text width (rough calculation)
                    if len(test_line) * 6 < page_width - 2 * margin:
                        current_line = test_line
                    else:
                        if current_line:
                            page.insert_text(
                                (margin, y_position),
                                current_line,
                                fontname=body_font,
                                fontsize=10,
                                color=(0, 0, 0)
                            )
                            y_position += line_height
                        current_line = word
                
                if current_line:
                    page.insert_text(
                        (margin, y_position),
                        current_line,
                        fontname=body_font,
                        fontsize=10,
                        color=(0, 0, 0)
                    )
                    y_position += line_height
        
        # Save the new PDF
        new_doc.save(output_pdf)
        new_doc.close()
        
        print(f"✅ Successfully created polished resume: {output_pdf}")
        
        # Show file sizes
        import os
        original_size = os.path.getsize(input_pdf)
        new_size = os.path.getsize(output_pdf)
        print(f"Original size: {original_size:,} bytes")
        print(f"New size: {new_size:,} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = create_polished_resume()
    if success:
        print("\n🎉 Your polished resume is ready!")
        print("The CATEGORY MANAGER section has been removed while maintaining professional formatting.")
    else:
        print("\n❌ Failed to create polished resume. Please check the error messages above.")
