import fitz

def thorough_percentage_removal():
    """
    More thorough removal of percentages
    """
    input_pdf = "Resume_Abhishek_IimL_NO_SPACE.pdf"
    output_pdf = "Resume_Abhishek_IimL_NO_PERCENTAGES.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find and remove all instances of the percentages
            percentages = ["67.7%", "73.5%", "67.7", "73.5"]
            
            for percentage in percentages:
                instances = page.search_for(percentage)
                
                for rect in instances:
                    print(f"Found '{percentage}' at: {rect}")
                    
                    # Create a larger white rectangle to ensure complete removal
                    expanded_rect = fitz.Rect(rect.x0 - 5, rect.y0 - 2, rect.x1 + 5, rect.y1 + 2)
                    page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    
                    print(f"✅ Thoroughly removed '{percentage}'")
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        
        # Double-check verification
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        print("\n=== VERIFICATION ===")
        checks = ["67.7%", "73.5%", "67.7", "73.5"]
        all_removed = True
        
        for check in checks:
            if check in verify_text:
                print(f"❌ Still found: {check}")
                all_removed = False
            else:
                print(f"✅ Removed: {check}")
        
        if all_removed:
            print("\n🎉 ALL PERCENTAGES SUCCESSFULLY REMOVED!")
        else:
            print("\n⚠️  Some percentages may still be present")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Thorough removal of education percentages...")
    thorough_percentage_removal()
