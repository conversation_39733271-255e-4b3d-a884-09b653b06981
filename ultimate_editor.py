import fitz

def create_ultimate_resume():
    """
    Final version: Preserve exact formatting and close the gap
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_ULTIMATE.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            if page_num == 0:  # Process first page where CATEGORY MANAGER is
                
                # Step 1: Find the CATEGORY MANAGER section boundaries
                category_start_y = None
                category_end_y = None
                
                # Search for CATEGORY MANAGER
                cm_instances = page.search_for("CATEGORY MANAGER")
                if cm_instances:
                    category_start_y = cm_instances[0].y0 - 5  # Start a bit above
                
                # Search for ASSOCIATE BUSINESS MANAGER (next section)
                abm_instances = page.search_for("ASSOCIATE BUSINESS MANAGER")
                if abm_instances:
                    category_end_y = abm_instances[0].y0 - 5  # End a bit above next section
                
                if category_start_y and category_end_y:
                    gap_height = category_end_y - category_start_y
                    print(f"Found CATEGORY MANAGER section: Y {category_start_y} to {category_end_y}")
                    print(f"Gap to close: {gap_height} points")
                    
                    # Step 2: Copy content above the CATEGORY MANAGER section
                    above_rect = fitz.Rect(0, 0, page.rect.width, category_start_y)
                    new_page.show_pdf_page(above_rect, doc, page_num, clip=above_rect)
                    print("Copied content above CATEGORY MANAGER")
                    
                    # Step 3: Copy content below and move it up to close the gap
                    below_rect = fitz.Rect(0, category_end_y, page.rect.width, page.rect.height)
                    target_rect = fitz.Rect(0, category_start_y, page.rect.width, page.rect.height - gap_height)
                    new_page.show_pdf_page(target_rect, doc, page_num, clip=below_rect)
                    print("Copied content below and moved up to close gap")
                    
                else:
                    print("Could not find section boundaries, copying entire page")
                    new_page.show_pdf_page(new_page.rect, doc, page_num)
                    
            else:
                # For other pages, copy as-is
                new_page.show_pdf_page(new_page.rect, doc, page_num)
                print(f"Copied page {page_num + 1} as-is")
        
        # Save result
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        
        # Verify
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        # Check removal
        checks = [
            ("CATEGORY MANAGER", "CATEGORY MANAGER" not in verify_text),
            ("Contlo technology", "Contlo technology" not in verify_text),
            ("Built 0 to 1 categories", "Built 0 to 1 categories" not in verify_text)
        ]
        
        for item, success in checks:
            status = "✅ REMOVED" if success else "❌ STILL PRESENT"
            print(f"{status}: {item}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Creating ultimate resume with perfect formatting and no gaps...")
    
    success = create_ultimate_resume()
    
    if success:
        print("\n🎉 ULTIMATE VERSION READY!")
        print("📄 Resume_Abhishek_IimL_ULTIMATE.pdf")
        print("✅ Original formatting preserved")
        print("✅ CATEGORY MANAGER section removed") 
        print("✅ Gap closed - content moved up")
        print("✅ Professional appearance maintained")
    else:
        print("\n❌ Failed to create ultimate version")
