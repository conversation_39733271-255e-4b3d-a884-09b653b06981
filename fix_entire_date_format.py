import fitz

def fix_entire_date_format():
    """
    Fix the entire date format by replacing the whole "( Apr 2023 - May 2024 )" 
    with the exact same format as "( Jul 2024 - Sep 2024 )"
    """
    input_pdf = "Resume_Abhishek_IimL_DONE.pdf"  # Start from the working version
    output_pdf = "Resume_Abhishek_IimL_IDENTICAL.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find the reference date format "( Jul 2024 - Sep 2024 )"
            reference_formatting = None
            jul_text = "( Jul 2024 - Sep 2024 )"
            jul_instances = page.search_for(jul_text)
            
            if jul_instances:
                ref_rect = jul_instances[0]
                print(f"Found reference: '{jul_text}' at {ref_rect}")
                
                # Get the exact formatting from the reference
                text_dict = page.get_text("dict")
                for block in text_dict["blocks"]:
                    if "lines" not in block:
                        continue
                    for line in block["lines"]:
                        for span in line["spans"]:
                            if jul_text in span["text"]:
                                reference_formatting = {
                                    'fontsize': span["size"],
                                    'color': span.get("color", 0),
                                    'flags': span.get("flags", 0),
                                    'font': span.get("font", "helv")
                                }
                                print(f"Reference formatting: {reference_formatting}")
                                break
            
            # Find our target date and replace the ENTIRE string
            if reference_formatting:
                # Look for "Apr 2023" to find our target date
                apr_instances = page.search_for("Apr 2023")
                
                for rect in apr_instances:
                    # Get the context to find the full date string
                    context_rect = fitz.Rect(rect.x0 - 10, rect.y0 - 2, rect.x1 + 100, rect.y1 + 2)
                    context = page.get_textbox(context_rect)
                    print(f"Found context: '{context.strip()}'")
                    
                    if "May 2024" in context:
                        print("Found our target date!")
                        
                        # Find the full date range in the context
                        full_date_rect = fitz.Rect(rect.x0 - 5, rect.y0 - 1, rect.x1 + 95, rect.y1 + 1)
                        
                        # White out the entire date area
                        page.draw_rect(full_date_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                        print("Whited out entire date area")
                        
                        # Convert color from reference
                        color = reference_formatting['color']
                        if isinstance(color, int) and color != 0:
                            r = ((color >> 16) & 0xFF) / 255.0
                            g = ((color >> 8) & 0xFF) / 255.0  
                            b = (color & 0xFF) / 255.0
                            color_rgb = (r, g, b)
                        else:
                            color_rgb = (0.35, 0.26, 0.47)
                        
                        # Insert the new date with EXACT same format as reference
                        text_point = fitz.Point(rect.x0 - 5, rect.y1 - 1)
                        new_date_text = "( Apr 2023 - May 2024 )"
                        
                        page.insert_text(
                            text_point,
                            new_date_text,
                            fontsize=reference_formatting['fontsize'],
                            color=color_rgb
                        )
                        
                        print(f"Inserted: '{new_date_text}' with exact reference formatting")
                        print(f"Font size: {reference_formatting['fontsize']}")
                        print(f"Color: {color_rgb}")
                        break
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Applied IDENTICAL formatting to entire date string")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Fixing ENTIRE date format to be identical...")
    success = fix_entire_date_format()
    
    if success:
        print("\n🎉 IDENTICAL FORMATTING APPLIED!")
        print("📄 Resume_Abhishek_IimL_IDENTICAL.pdf")
        print("The date format should now be EXACTLY the same!")
    else:
        print("\n❌ Failed to fix formatting")
