import fitz

def surgical_edit_approach():
    """
    Surgical approach: Copy the original PDF exactly and make minimal changes
    by copying existing formatted text from other parts of the document
    """
    input_pdf = "Resume_Abhishek_IimL_ULTIMATE.pdf"  # Start with the clean version
    output_pdf = "Resume_Abhishek_IimL_SURGICAL.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Step 1: Find and white out "Dec 2023"
            dec_instances = page.search_for("Dec 2023")
            for rect in dec_instances:
                print(f"Found 'Dec 2023' at: {rect}")
                # White out just "Dec 2023"
                page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                print("Whited out 'Dec 2023'")
            
            # Step 2: Find existing "May 2024" text from another part of the document
            # and copy its exact formatting
            may_instances = page.search_for("May 2024")
            if may_instances:
                # Get the formatting from existing "May 2024" text
                source_rect = may_instances[0]
                print(f"Found existing 'May 2024' at: {source_rect}")
                
                # Extract the exact text with formatting
                blocks = page.get_text("dict")
                source_formatting = None
                
                for block in blocks["blocks"]:
                    if "lines" not in block:
                        continue
                    for line in block["lines"]:
                        for span in line["spans"]:
                            span_rect = fitz.Rect(span["bbox"])
                            if span_rect.intersects(source_rect) and "May 2024" in span["text"]:
                                source_formatting = {
                                    'fontsize': span['size'],
                                    'color': span.get('color', 0),
                                    'flags': span.get('flags', 0),
                                    'font': span.get('font', 'helv')
                                }
                                print(f"Extracted formatting: {source_formatting}")
                                break
                
                # Now place "May 2024" where "Dec 2023" was
                if source_formatting and dec_instances:
                    target_rect = dec_instances[0]
                    text_point = fitz.Point(target_rect.x0, target_rect.y1 - 1)
                    
                    # Convert color if needed
                    color = source_formatting['color']
                    if isinstance(color, int) and color != 0:
                        r = ((color >> 16) & 0xFF) / 255.0
                        g = ((color >> 8) & 0xFF) / 255.0  
                        b = (color & 0xFF) / 255.0
                        color = (r, g, b)
                    else:
                        color = (0.35, 0.26, 0.47)  # Default gray
                    
                    page.insert_text(
                        text_point,
                        "May 2024",
                        fontsize=source_formatting['fontsize'],
                        color=color
                    )
                    print("Inserted 'May 2024' with copied formatting")
            
            # Step 3: Remove education percentages
            percentages = ["67.7%", "73.5%"]
            for percentage in percentages:
                instances = page.search_for(percentage)
                for rect in instances:
                    print(f"Removing percentage: {percentage}")
                    page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Used surgical editing approach")
        print("✅ Copied existing formatting from document")
        print("✅ Minimal changes to preserve original appearance")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def alternative_copy_approach():
    """
    Alternative: Start fresh from original and make very precise edits
    """
    original_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_COPY_EDIT.pdf"
    
    try:
        # Open original
        original_doc = fitz.open(original_pdf)
        
        # Create new document by copying original exactly
        new_doc = fitz.open()
        
        for page_num in range(len(original_doc)):
            page = original_doc[page_num]
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            # Copy the entire page exactly
            new_page.show_pdf_page(new_page.rect, original_doc, page_num)
            
            # Now make only the specific edits needed:
            
            # 1. Remove CATEGORY MANAGER section (we know this works)
            category_areas = [
                fitz.Rect(30, 570, 570, 660),  # Approximate area
            ]
            
            for rect in category_areas:
                new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                print("Removed CATEGORY MANAGER section")
            
            # 2. Change just "Dec" to "May" in the date
            dec_instances = new_page.search_for("Dec")
            for rect in dec_instances:
                # Check if this is in a date context
                context = new_page.get_textbox(fitz.Rect(rect.x0 - 20, rect.y0, rect.x1 + 20, rect.y1))
                if "2023" in context:
                    print(f"Found 'Dec' in date context: {context}")
                    # White out just "Dec"
                    new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    # Insert "May" in the same spot
                    text_point = fitz.Point(rect.x0, rect.y1 - 1)
                    new_page.insert_text(text_point, "May", fontsize=8, color=(0.35, 0.26, 0.47))
                    print("Changed 'Dec' to 'May'")
            
            # 3. Remove percentages
            for percentage in ["67.7%", "73.5%"]:
                instances = new_page.search_for(percentage)
                for rect in instances:
                    new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    print(f"Removed {percentage}")
        
        new_doc.save(output_pdf)
        new_doc.close()
        original_doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Copied original exactly and made minimal edits")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Trying surgical editing approach...")
    
    success1 = surgical_edit_approach()
    
    if not success1:
        print("\nTrying alternative copy approach...")
        alternative_copy_approach()
    
    print("\nCheck both output files to see which looks better!")
