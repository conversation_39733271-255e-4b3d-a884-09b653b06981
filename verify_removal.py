import pdfplumber

def extract_text(pdf_path):
    text = ""
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
    return text

# Extract text from both PDFs
original_text = extract_text("Resume_Abhishek_IimL.pdf")
cleaned_text = extract_text("Resume_Abhishek_IimL_cleaned.pdf")

print("=== VERIFICATION ===")
print(f"Original text length: {len(original_text)} characters")
print(f"Cleaned text length: {len(cleaned_text)} characters")
print(f"Removed: {len(original_text) - len(cleaned_text)} characters")

# Check if CATEGORY MANAGER section exists in cleaned version
if "CATEGORY MANAGER" in cleaned_text:
    print("\n❌ ERROR: CATEGORY MANAGER section still exists in cleaned PDF!")
    # Find where it appears
    start = cleaned_text.find("CATEGORY MANAGER")
    if start != -1:
        print("Found at position:", start)
        print("Context:", cleaned_text[max(0, start-100):start+200])
else:
    print("\n✅ SUCCESS: CATEGORY MANAGER section has been removed!")

# Check if Contlo technology is still there
if "Contlo technology pvt ltd" in cleaned_text:
    print("\n❌ ERROR: 'Contlo technology pvt ltd' still exists in cleaned PDF!")
else:
    print("\n✅ SUCCESS: 'Contlo technology pvt ltd' has been removed!")

# Show the work experience section to verify structure
work_exp_start = cleaned_text.find("WORK EXPERIENCE")
if work_exp_start != -1:
    work_section = cleaned_text[work_exp_start:work_exp_start+1000]
    print("\n=== WORK EXPERIENCE SECTION (first 1000 chars) ===")
    print(work_section)
