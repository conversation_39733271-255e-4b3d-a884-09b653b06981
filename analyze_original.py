import fitz

def analyze_original_formatting():
    """
    Carefully analyze the original PDF to understand the exact date formatting
    """
    original_pdf = "Resume_Abhishek_IimL.pdf"
    
    try:
        doc = fitz.open(original_pdf)
        
        print("=== ANALYZING ORIGINAL DATE FORMATTING ===")
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Look for all date patterns in the original
            date_patterns = [
                "Jul 2024 - Sep 2024",
                "Apr 2023 - Dec 2023", 
                "Jul 2021 - Mar 2023",
                "2019 - 2021",
                "2012 - 2016"
            ]
            
            blocks = page.get_text("dict")
            
            for pattern in date_patterns:
                instances = page.search_for(pattern)
                if instances:
                    print(f"\nFound date pattern: '{pattern}'")
                    for rect in instances:
                        print(f"  Position: {rect}")
                        
                        # Find the exact text formatting for this date
                        for block in blocks["blocks"]:
                            if "lines" not in block:
                                continue
                            for line in block["lines"]:
                                for span in line["spans"]:
                                    span_rect = fitz.Rect(span["bbox"])
                                    if span_rect.intersects(rect):
                                        print(f"  Text: '{span['text']}'")
                                        print(f"  Font size: {span['size']}")
                                        print(f"  Font flags: {span['flags']}")
                                        print(f"  Color: {span.get('color', 'default')}")
                                        print(f"  Font: {span.get('font', 'default')}")
                                        
                                        # Check surrounding text for context
                                        surrounding = page.get_textbox(fitz.Rect(rect.x0 - 20, rect.y0 - 5, rect.x1 + 20, rect.y1 + 5))
                                        print(f"  Context: '{surrounding.strip()}'")
                                        break
        
        doc.close()
        
    except Exception as e:
        print(f"Error: {e}")

def get_exact_context():
    """
    Get the exact context around the ASSOCIATE BUSINESS MANAGER section
    """
    original_pdf = "Resume_Abhishek_IimL.pdf"
    
    try:
        doc = fitz.open(original_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find ASSOCIATE BUSINESS MANAGER section
            abm_instances = page.search_for("ASSOCIATE BUSINESS MANAGER")
            if abm_instances:
                rect = abm_instances[0]
                print(f"\n=== ASSOCIATE BUSINESS MANAGER SECTION ===")
                
                # Get a larger area around this section to see the full formatting
                context_rect = fitz.Rect(rect.x0 - 50, rect.y0 - 10, 
                                       page.rect.width - 50, rect.y1 + 30)
                context_text = page.get_textbox(context_rect)
                print("Full section context:")
                print(context_text)
                
                # Also get the detailed formatting
                blocks = page.get_text("dict")
                for block in blocks["blocks"]:
                    if "lines" not in block:
                        continue
                    block_rect = fitz.Rect(block["bbox"])
                    if block_rect.intersects(context_rect):
                        for line in block["lines"]:
                            line_text = ""
                            for span in line["spans"]:
                                line_text += span["text"]
                            if "Apr 2023" in line_text or "Dec 2023" in line_text:
                                print(f"\nDate line: '{line_text}'")
                                for span in line["spans"]:
                                    print(f"  Span: '{span['text']}' - Size: {span['size']}, Color: {span.get('color')}")
        
        doc.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    analyze_original_formatting()
    get_exact_context()
