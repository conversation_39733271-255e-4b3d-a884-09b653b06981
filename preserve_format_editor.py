import fitz

def preserve_original_format():
    """
    Keep EXACT original formatting and just remove the CATEGORY MANAGER section
    by copying everything pixel-perfect and only redacting specific areas
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_FORMATTED.pdf"
    
    try:
        # Open original PDF
        doc = fitz.open(input_pdf)
        
        # Create new document with exact same pages
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Create new page with EXACT same dimensions
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            # Copy the ENTIRE page exactly as it is (preserves all formatting, colors, fonts)
            new_page.show_pdf_page(new_page.rect, doc, page_num)
            
            print(f"Copied page {page_num + 1} with original formatting preserved")
            
            # Now find and precisely remove only the CATEGORY MANAGER content
            # Search for the exact text positions
            search_terms = [
                "CATEGORY MANAGER",
                "Jan 2024 - May 2024", 
                "Contlo technology pvt ltd"
            ]
            
            removal_areas = []
            
            for term in search_terms:
                instances = page.search_for(term)
                for rect in instances:
                    # Expand the rectangle slightly to ensure complete coverage
                    expanded = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                    removal_areas.append(expanded)
                    print(f"Found '{term}' at {rect}")
            
            # Also find the bullet points by searching for their content
            bullet_searches = [
                "Built 0 to 1 categories for 10+ AI-driven virtual employees",
                "Streamlined operations for the team of 30+ by standardizing",
                "Pivotal in defining KPIs, metrics, and visualizing them",
                "Hired, onboarded, and trained manpower for 10+ categories"
            ]
            
            for bullet in bullet_searches:
                instances = page.search_for(bullet[:30])  # Search first part
                for rect in instances:
                    # For bullet points, extend the rectangle to cover the full line
                    expanded = fitz.Rect(rect.x0 - 5, rect.y0 - 1, 
                                       page.rect.width - 50, rect.y1 + 1)
                    removal_areas.append(expanded)
                    print(f"Found bullet point at {rect}")
            
            # Apply white rectangles to remove the content
            for rect in removal_areas:
                new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                print(f"Removed area: {rect}")
            
            # If we found content to remove, also remove the gap
            if removal_areas and page_num == 0:  # Assuming it's on first page
                # Find the overall area that was removed
                min_y = min(rect.y0 for rect in removal_areas)
                max_y = max(rect.y1 for rect in removal_areas)
                
                print(f"CATEGORY MANAGER section spans from Y {min_y} to Y {max_y}")
                
                # Create a white rectangle to cover the entire section area
                section_rect = fitz.Rect(30, min_y - 5, page.rect.width - 30, max_y + 5)
                new_page.draw_rect(section_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                print(f"Covered entire section area: {section_rect}")
        
        # Save the result
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Original formatting, colors, and fonts preserved")
        print("✅ Only CATEGORY MANAGER section removed")
        
        # Quick verification
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        if "CATEGORY MANAGER" not in verify_text:
            print("✅ CATEGORY MANAGER text successfully removed")
        else:
            print("⚠️  Some CATEGORY MANAGER text may still be visible")
            
        if "Contlo technology" not in verify_text:
            print("✅ Contlo technology successfully removed")
        else:
            print("⚠️  Some Contlo technology text may still be visible")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Preserving original format while removing CATEGORY MANAGER section...")
    print("This will keep all colors, fonts, and layout exactly as the original.")
    
    success = preserve_original_format()
    
    if success:
        print("\n🎉 DONE!")
        print("📄 Resume_Abhishek_IimL_FORMATTED.pdf")
        print("Your resume now has the exact original formatting with the unwanted section removed.")
    else:
        print("\n❌ Failed. Check error messages above.")
