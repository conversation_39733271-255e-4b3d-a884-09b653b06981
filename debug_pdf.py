import pdfplumber

def extract_and_show_text(pdf_path):
    """Extract and display the full text to understand the structure"""
    text = ""
    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            page_text = page.extract_text()
            if page_text:
                print(f"=== PAGE {page_num + 1} ===")
                print(repr(page_text))  # Using repr to see exact characters
                print("=" * 50)
                text += page_text + "\n"
    return text

if __name__ == "__main__":
    text = extract_and_show_text("Resume_Abhishek_IimL.pdf")
    
    print("\n\nFULL TEXT:")
    print("=" * 80)
    print(text)
