import fitz

def check_pdf_quality(pdf_path):
    """Check what the polished PDF actually looks like"""
    try:
        doc = fitz.open(pdf_path)
        print(f"=== CHECKING {pdf_path} ===")
        print(f"Number of pages: {len(doc)}")
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\nPage {page_num + 1}:")
            print(f"Page size: {page.rect.width} x {page.rect.height}")
            
            # Get text to see formatting
            text = page.get_text()
            print("Text content (first 500 chars):")
            print(text[:500])
            print("..." if len(text) > 500 else "")
            
        doc.close()
        
    except Exception as e:
        print(f"Error checking PDF: {e}")

if __name__ == "__main__":
    check_pdf_quality("Resume_Abhishek_IimL_polished.pdf")
    print("\n" + "="*50)
    print("ORIGINAL for comparison:")
    check_pdf_quality("Resume_Abhishek_IimL.pdf")
