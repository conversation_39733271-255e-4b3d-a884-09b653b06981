import fitz

def remove_unnecessary_space():
    """
    Remove unnecessary space at the end of first page
    """
    input_pdf = "Resume_Abhishek_IimL_IDENTICAL.pdf"  # Use the selected PDF as reference
    output_pdf = "Resume_Abhishek_IimL_COMPACT.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            if page_num == 0:  # First page - remove unnecessary space
                print("Processing first page to remove unnecessary space...")
                
                # Get all content on the page
                blocks = page.get_text("dict")
                
                # Find the lowest Y coordinate with actual content
                lowest_content_y = 0
                for block in blocks["blocks"]:
                    if "lines" in block:
                        block_bottom = block["bbox"][3]  # Bottom Y coordinate
                        if block_bottom > lowest_content_y:
                            lowest_content_y = block_bottom
                
                print(f"Lowest content found at Y: {lowest_content_y}")
                
                # Calculate new page height (add some margin after content)
                margin_bottom = 50  # Small margin at bottom
                new_height = lowest_content_y + margin_bottom
                
                # Don't make it smaller than a reasonable minimum
                min_height = 600
                if new_height < min_height:
                    new_height = min_height
                
                print(f"New page height: {new_height} (original: {page.rect.height})")
                
                # Create new page with reduced height
                new_page = new_doc.new_page(width=page.rect.width, height=new_height)
                
                # Copy content from original page to new page
                # Use a clip rectangle to copy only the content area
                content_rect = fitz.Rect(0, 0, page.rect.width, new_height)
                new_page.show_pdf_page(new_page.rect, doc, page_num, clip=content_rect)
                
                print(f"Removed {page.rect.height - new_height} points of unnecessary space")
                
            else:
                # For other pages, copy as-is
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
                new_page.show_pdf_page(new_page.rect, doc, page_num)
                print(f"Copied page {page_num + 1} as-is")
        
        # Save the result
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Removed unnecessary space from end of first page")
        print("✅ Maintained all original formatting and content")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def alternative_space_removal():
    """
    Alternative approach: Move content up to eliminate space
    """
    input_pdf = "Resume_Abhishek_IimL_IDENTICAL.pdf"
    output_pdf = "Resume_Abhishek_IimL_TIGHT.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            if page_num == 0:  # First page
                print("Compacting first page content...")
                
                # Copy the page content but compress it vertically
                # Scale factor to compress content
                scale_factor = 0.9  # Compress to 90% of original height
                
                # Create a transformation matrix for scaling
                matrix = fitz.Matrix(1.0, scale_factor)  # Scale Y by 90%
                
                # Apply the transformation
                new_page.show_pdf_page(new_page.rect, doc, page_num, matrix=matrix)
                
                print(f"Compressed first page content by {(1-scale_factor)*100}%")
                
            else:
                # Copy other pages normally
                new_page.show_pdf_page(new_page.rect, doc, page_num)
        
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"\n✅ Alternative version created: {output_pdf}")
        return True
        
    except Exception as e:
        print(f"❌ Error in alternative approach: {e}")
        return False

if __name__ == "__main__":
    print("Removing unnecessary space from end of first page...")
    print("Using Resume_Abhishek_IimL_IDENTICAL.pdf as reference")
    
    success1 = remove_unnecessary_space()
    
    if not success1:
        print("\nTrying alternative approach...")
        alternative_space_removal()
    
    print("\nCheck the output files to see which version looks better!")
