import fitz

def verify_manual_pdf():
    """Verify the manual PDF looks good"""
    
    files_to_check = [
        ("Original", "Resume_Abhishek_IimL.pdf"),
        ("Manual Edit", "Resume_Abhishek_IimL_MANUAL.pdf")
    ]
    
    for name, filename in files_to_check:
        try:
            doc = fitz.open(filename)
            print(f"\n=== {name}: {filename} ===")
            
            full_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                full_text += text
            
            # Check for removed content
            checks = [
                ("CATEGORY MANAGER", "CATEGORY MANAGER" in full_text),
                ("Contlo technology", "Contlo technology" in full_text),
                ("Built 0 to 1 categories", "Built 0 to 1 categories" in full_text),
                ("Streamlined operations", "Streamlined operations" in full_text)
            ]
            
            for item, found in checks:
                status = "❌ FOUND" if found else "✅ REMOVED"
                print(f"{status}: {item}")
            
            # Show first few lines to verify structure
            lines = full_text.split('\n')[:10]
            print(f"\nFirst 10 lines:")
            for i, line in enumerate(lines, 1):
                if line.strip():
                    print(f"{i:2d}: {line.strip()}")
            
            doc.close()
            
        except Exception as e:
            print(f"Error checking {filename}: {e}")

if __name__ == "__main__":
    verify_manual_pdf()
