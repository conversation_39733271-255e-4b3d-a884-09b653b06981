import fitz  # PyMuPDF
import re

def remove_category_manager_from_pdf(input_pdf, output_pdf):
    """
    Remove the CATEGORY MANAGER section from PDF while preserving original formatting
    """
    # Open the PDF
    doc = fitz.open(input_pdf)
    
    # Create a new PDF document
    new_doc = fitz.open()
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        
        # Get all text blocks with their positions
        blocks = page.get_text("dict")
        
        # Find and remove CATEGORY MANAGER section
        filtered_blocks = []
        skip_until_next_section = False
        
        for block in blocks["blocks"]:
            if "lines" in block:
                block_text = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        block_text += span["text"] + " "
                
                block_text = block_text.strip()
                
                # Check if this is the start of CATEGORY MANAGER section
                if "CATEGORY MANAGER" in block_text and "Jan 2024" in block_text:
                    skip_until_next_section = True
                    continue
                
                # Check if this is the start of the next section (ASSOCIATE BUSINESS MANAGER)
                if skip_until_next_section and "ASSOCIATE BUSINESS MANAGER" in block_text:
                    skip_until_next_section = False
                    filtered_blocks.append(block)
                    continue
                
                # Skip blocks that are part of CATEGORY MANAGER section
                if skip_until_next_section:
                    # Check if it's content related to Contlo technology
                    if any(keyword in block_text.lower() for keyword in [
                        "contlo technology", "built 0 to 1 categories", 
                        "streamlined operations", "pivotal in defining kpis",
                        "hired, onboarded, and trained"
                    ]):
                        continue
                    # If it's a bullet point or continuation
                    if block_text.startswith("•") or block_text.startswith("-") or len(block_text) > 50:
                        continue
                
                # Add block if not skipping
                if not skip_until_next_section:
                    filtered_blocks.append(block)
            else:
                # Non-text blocks (images, etc.)
                if not skip_until_next_section:
                    filtered_blocks.append(block)
        
        # Create new page with filtered content
        new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
        
        # Recreate the page content without the CATEGORY MANAGER section
        for block in filtered_blocks:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        # Get text properties
                        text = span["text"]
                        font_size = span["size"]
                        font_flags = span["flags"]
                        color = span.get("color", 0)
                        
                        # Calculate position
                        bbox = span["bbox"]
                        point = fitz.Point(bbox[0], bbox[1] + font_size * 0.8)
                        
                        # Insert text with original formatting
                        new_page.insert_text(
                            point, 
                            text, 
                            fontsize=font_size,
                            color=color,
                            flags=font_flags
                        )
            elif "type" in block and block["type"] == 1:  # Image block
                # Handle images if needed
                pass
    
    # Save the new PDF
    new_doc.save(output_pdf)
    new_doc.close()
    doc.close()
    
    print(f"Successfully created {output_pdf} with CATEGORY MANAGER section removed")

def simple_text_based_removal(input_pdf, output_pdf):
    """
    Simpler approach: extract all text, remove the section, and recreate
    """
    doc = fitz.open(input_pdf)
    
    # Extract all text
    full_text = ""
    for page in doc:
        full_text += page.get_text() + "\n"
    
    doc.close()
    
    # Remove the CATEGORY MANAGER section using regex
    # Pattern to match from "CATEGORY MANAGER" to before "ASSOCIATE BUSINESS MANAGER"
    pattern = r'CATEGORY MANAGER.*?(?=ASSOCIATE BUSINESS MANAGER)'
    cleaned_text = re.sub(pattern, '', full_text, flags=re.DOTALL)
    
    # Clean up extra whitespace
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    
    # Create new PDF with cleaned text
    new_doc = fitz.open()
    page = new_doc.new_page()
    
    # Insert cleaned text
    page.insert_text((72, 72), cleaned_text, fontsize=11)
    
    new_doc.save(output_pdf)
    new_doc.close()
    
    print(f"Successfully created {output_pdf} with simplified formatting")

if __name__ == "__main__":
    input_file = "Resume_Abhishek_IimL.pdf"
    output_file = "Resume_Abhishek_IimL_polished.pdf"
    
    try:
        # Try the advanced method first
        print("Attempting advanced PDF editing with preserved formatting...")
        remove_category_manager_from_pdf(input_file, output_file)
    except Exception as e:
        print(f"Advanced method failed: {e}")
        print("Falling back to simple text-based method...")
        try:
            simple_text_based_removal(input_file, output_file)
        except Exception as e2:
            print(f"Simple method also failed: {e2}")
            print("Please try using a different PDF editing tool.")
