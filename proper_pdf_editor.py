import fitz  # PyMuPDF

def remove_category_manager_preserve_format():
    """
    Remove CATEGORY MANAGER section while preserving EXACT original formatting
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_FINAL.pdf"
    
    try:
        # Open the original PDF
        doc = fitz.open(input_pdf)
        
        # Create a new document
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Get all text blocks with their exact positions and formatting
            blocks = page.get_text("dict")
            
            # Create new page with same dimensions
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            # Process each block
            for block in blocks["blocks"]:
                if "lines" not in block:
                    continue
                    
                # Get the text content of this block
                block_text = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        block_text += span["text"] + " "
                block_text = block_text.strip()
                
                # Skip blocks that contain CATEGORY MANAGER content
                skip_block = False
                
                # Check for CATEGORY MANAGER section markers
                if any(phrase in block_text for phrase in [
                    "CATEGORY MANAGER",
                    "Jan 2024 - May 2024",
                    "Contlo technology pvt ltd",
                    "Built 0 to 1 categories for 10+ AI-driven virtual employees",
                    "Streamlined operations for the team of 30+",
                    "Pivotal in defining KPIs, metrics, and visualizing them",
                    "Hired, onboarded, and trained manpower for 10+ categories"
                ]):
                    skip_block = True
                    print(f"Skipping block: {block_text[:50]}...")
                
                # If this block should be kept, recreate it exactly
                if not skip_block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            # Get exact formatting
                            text = span["text"]
                            if not text.strip():
                                continue
                                
                            font_size = span["size"]
                            font_flags = span["flags"]
                            color = span.get("color", 0)
                            font_name = span.get("font", "helv")
                            
                            # Get exact position
                            bbox = span["bbox"]
                            point = fitz.Point(bbox[0], bbox[1] + font_size * 0.8)
                            
                            # Insert text with exact original formatting
                            try:
                                new_page.insert_text(
                                    point,
                                    text,
                                    fontname=font_name,
                                    fontsize=font_size,
                                    color=color,
                                    flags=font_flags
                                )
                            except:
                                # Fallback to basic formatting if font issues
                                new_page.insert_text(
                                    point,
                                    text,
                                    fontsize=font_size,
                                    color=color
                                )
        
        # Save the result
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"✅ SUCCESS: Created {output_pdf}")
        print("✅ CATEGORY MANAGER section removed while preserving exact formatting")
        
        # Verify the result
        verify_doc = fitz.open(output_pdf)
        text_check = ""
        for page in verify_doc:
            text_check += page.get_text()
        verify_doc.close()
        
        if "CATEGORY MANAGER" in text_check:
            print("❌ WARNING: CATEGORY MANAGER still found in output")
        else:
            print("✅ VERIFIED: CATEGORY MANAGER section successfully removed")
            
        if "Contlo technology" in text_check:
            print("❌ WARNING: Contlo technology still found in output")
        else:
            print("✅ VERIFIED: Contlo technology successfully removed")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Creating properly formatted PDF with CATEGORY MANAGER section removed...")
    success = remove_category_manager_preserve_format()
    
    if success:
        print("\n🎉 DONE! Your resume is ready:")
        print("📄 Resume_Abhishek_IimL_FINAL.pdf")
        print("\nThis version preserves the exact original formatting while removing only the unwanted section.")
    else:
        print("\n❌ Failed to create the PDF. Check error messages above.")
