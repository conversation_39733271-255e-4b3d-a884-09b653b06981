import fitz

def remove_space_from_first_page():
    """
    Remove unnecessary space at the end of first page
    """
    input_pdf = "Resume_Abhishek_IimL_ULTIMATE.pdf"
    output_pdf = "Resume_Abhishek_IimL_NO_SPACE.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            if page_num == 0:  # First page - remove space at end
                print("Processing first page to remove end space...")
                
                # Find the actual content boundaries
                blocks = page.get_text("dict")
                
                # Find the lowest Y coordinate with actual content
                lowest_content_y = 0
                highest_content_y = page.rect.height
                
                for block in blocks["blocks"]:
                    if "lines" in block and block["lines"]:
                        block_top = block["bbox"][1]    # Top Y coordinate
                        block_bottom = block["bbox"][3] # Bottom Y coordinate
                        
                        if block_bottom > lowest_content_y:
                            lowest_content_y = block_bottom
                        if block_top < highest_content_y:
                            highest_content_y = block_top
                
                print(f"Content spans from Y: {highest_content_y} to Y: {lowest_content_y}")
                print(f"Original page height: {page.rect.height}")
                
                # Calculate new page height with small bottom margin
                bottom_margin = 30  # Small margin at bottom
                new_height = lowest_content_y + bottom_margin
                
                # Ensure minimum reasonable height
                min_height = 700
                if new_height < min_height:
                    new_height = min_height
                
                print(f"New page height: {new_height}")
                space_removed = page.rect.height - new_height
                print(f"Space to be removed: {space_removed} points")
                
                # Create new page with reduced height
                new_page = new_doc.new_page(width=page.rect.width, height=new_height)
                
                # Copy all content from original page
                content_area = fitz.Rect(0, 0, page.rect.width, new_height)
                new_page.show_pdf_page(new_page.rect, doc, page_num, clip=content_area)
                
                print(f"✅ Removed {space_removed:.1f} points of space from first page")
                
            else:
                # Copy other pages as-is
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
                new_page.show_pdf_page(new_page.rect, doc, page_num)
                print(f"Copied page {page_num + 1} unchanged")
        
        # Save the result
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Removed unnecessary space from end of first page")
        print("✅ All formatting and content preserved")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Removing space from end of first page...")
    success = remove_space_from_first_page()
    
    if success:
        print("\n🎉 SPACE REMOVED!")
        print("📄 Resume_Abhishek_IimL_NO_SPACE.pdf")
        print("The first page is now more compact with no unnecessary space at the end.")
    else:
        print("\n❌ Failed to remove space")
