import pdfplumber
import re
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
import os

def extract_pdf_text(pdf_path):
    """Extract text from PDF file"""
    text = ""
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
    return text

def remove_category_manager_section(text):
    """Remove the CATEGORY MANAGER section from the text"""
    # Find the start of the CATEGORY MANAGER section
    start_pattern = r'CATEGORY MANAGER \( Jan 2024 - May 2024 \)'

    # Find the start of the next section (ASSOCIATE BUSINESS MANAGER)
    end_pattern = r'ASSOCIATE BUSINESS MANAGER - CATEGORY \( Apr 2023 - Dec 2023 \)'

    start_match = re.search(start_pattern, text)
    end_match = re.search(end_pattern, text)

    if start_match and end_match:
        # Remove everything from the start of CATEGORY MANAGER to the start of the next section
        before_section = text[:start_match.start()]
        after_section = text[end_match.start():]
        cleaned_text = before_section + after_section

        # Clean up extra whitespace
        cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n', cleaned_text)
        cleaned_text = cleaned_text.strip()

        return cleaned_text
    else:
        print("Could not find the CATEGORY MANAGER section to remove")
        return text

def create_pdf_from_text(text, output_path):
    """Create a new PDF from the cleaned text"""
    doc = SimpleDocTemplate(output_path, pagesize=letter,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=18)
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Create custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=12,
        alignment=TA_CENTER,
        fontName='Helvetica-Bold'
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=12,
        spaceAfter=6,
        spaceBefore=12,
        fontName='Helvetica-Bold',
        alignment=TA_LEFT
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        alignment=TA_LEFT,
        fontName='Helvetica'
    )
    
    bullet_style = ParagraphStyle(
        'CustomBullet',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=3,
        leftIndent=20,
        bulletIndent=10,
        alignment=TA_LEFT,
        fontName='Helvetica'
    )
    
    # Build the story
    story = []
    
    # Split text into lines
    lines = text.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            story.append(Spacer(1, 6))
            continue
            
        # Check if it's a title (all caps, short line)
        if line.isupper() and len(line) < 50 and not line.startswith('•'):
            story.append(Paragraph(line, title_style))
        # Check if it's a section heading (starts with capital, ends with date range or company)
        elif (line[0].isupper() and 
              (re.search(r'\d{4}', line) or 
               any(word in line.lower() for word in ['pvt', 'ltd', 'inc', 'corp', 'company']))):
            story.append(Paragraph(line, heading_style))
        # Check if it's a bullet point
        elif line.startswith('•') or line.startswith('-') or line.startswith('*'):
            story.append(Paragraph(line, bullet_style))
        # Regular text
        else:
            story.append(Paragraph(line, normal_style))
    
    # Build PDF
    doc.build(story)

def main():
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_cleaned.pdf"
    
    print("Extracting text from PDF...")
    text = extract_pdf_text(input_pdf)
    
    print("Original text preview:")
    print("=" * 50)
    print(text[:1000] + "..." if len(text) > 1000 else text)
    print("=" * 50)
    
    print("\nRemoving CATEGORY MANAGER section...")
    cleaned_text = remove_category_manager_section(text)
    
    print("Cleaned text preview:")
    print("=" * 50)
    print(cleaned_text[:1000] + "..." if len(cleaned_text) > 1000 else cleaned_text)
    print("=" * 50)
    
    print(f"\nCreating new PDF: {output_pdf}")
    create_pdf_from_text(cleaned_text, output_pdf)
    
    print(f"Done! Cleaned PDF saved as: {output_pdf}")
    
    # Show file sizes for comparison
    original_size = os.path.getsize(input_pdf)
    new_size = os.path.getsize(output_pdf)
    print(f"\nFile sizes:")
    print(f"Original: {original_size:,} bytes")
    print(f"Cleaned:  {new_size:,} bytes")

if __name__ == "__main__":
    main()
