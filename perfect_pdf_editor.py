from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
import fitz  # For text extraction and positioning
import io

def create_perfect_resume():
    """
    Create a perfect resume by copying the original and removing specific content areas
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_PERFECT.pdf"
    
    try:
        # First, let's identify exactly what needs to be removed
        doc = fitz.open(input_pdf)
        
        removal_areas = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Search for specific text that needs to be removed
            search_terms = [
                "CATEGORY MANAGER ( Jan 2024 - May 2024 )",
                "Contlo technology pvt ltd",
                "Built 0 to 1 categories for 10+ AI-driven virtual employees",
                "Streamlined operations for the team of 30+",
                "Pivotal in defining KPIs, metrics, and visualizing them",
                "Hired, onboarded, and trained manpower for 10+ categories"
            ]
            
            for term in search_terms:
                instances = page.search_for(term)
                for inst in instances:
                    removal_areas.append((page_num, inst))
                    print(f"Found '{term[:30]}...' at {inst}")
        
        # Now create a new PDF by copying the original and redacting specific areas
        reader = PdfReader(input_pdf)
        writer = PdfWriter()
        
        for page_num, page in enumerate(reader.pages):
            # Copy the page
            new_page = page
            
            # Apply redactions for this page
            page_removals = [area for pnum, area in removal_areas if pnum == page_num]
            
            if page_removals:
                print(f"Applying {len(page_removals)} redactions to page {page_num + 1}")
                
                # Convert to fitz for redaction
                page_doc = fitz.open()
                temp_page = page_doc.new_page(width=float(page.mediabox.width), 
                                            height=float(page.mediabox.height))
                
                # Copy original content
                pdf_bytes = io.BytesIO()
                temp_writer = PdfWriter()
                temp_writer.add_page(page)
                temp_writer.write(pdf_bytes)
                pdf_bytes.seek(0)
                
                temp_doc = fitz.open(stream=pdf_bytes.read(), filetype="pdf")
                temp_page.show_pdf_page(temp_page.rect, temp_doc, 0)
                
                # Apply redactions (white rectangles)
                for rect in page_removals:
                    # Expand the rectangle slightly to ensure complete coverage
                    expanded_rect = fitz.Rect(rect.x0 - 5, rect.y0 - 2, rect.x1 + 5, rect.y1 + 2)
                    temp_page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1))
                
                # Convert back to pypdf format
                redacted_bytes = temp_page.get_pixmap().tobytes("pdf")
                redacted_reader = PdfReader(io.BytesIO(redacted_bytes))
                new_page = redacted_reader.pages[0]
                
                temp_doc.close()
                page_doc.close()
            
            writer.add_page(new_page)
        
        # Save the result
        with open(output_pdf, 'wb') as output_file:
            writer.write(output_file)
        
        doc.close()
        
        print(f"✅ Created: {output_pdf}")
        
        # Verify
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        removed_items = []
        if "CATEGORY MANAGER" not in verify_text:
            removed_items.append("CATEGORY MANAGER")
        if "Contlo technology" not in verify_text:
            removed_items.append("Contlo technology")
        if "Built 0 to 1 categories" not in verify_text:
            removed_items.append("Built 0 to 1 categories")
        
        if removed_items:
            print(f"✅ Successfully removed: {', '.join(removed_items)}")
        else:
            print("⚠️  Some content may still be visible")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def manual_coordinate_removal():
    """
    Manually remove content by coordinates if automatic detection fails
    """
    input_pdf = "Resume_Abhishek_IimL.pdf"
    output_pdf = "Resume_Abhishek_IimL_MANUAL.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        # Create new document
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            
            # Copy the entire page
            new_page.show_pdf_page(new_page.rect, doc, page_num)
            
            # Manually define areas to remove (you may need to adjust these coordinates)
            if page_num == 0:  # Assuming CATEGORY MANAGER is on first page
                # These are approximate coordinates - may need adjustment
                removal_rects = [
                    fitz.Rect(30, 570, 570, 660),  # Main CATEGORY MANAGER section
                ]
                
                for rect in removal_rects:
                    new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                    print(f"Removed area: {rect}")
        
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"✅ Created manual version: {output_pdf}")
        return True
        
    except Exception as e:
        print(f"❌ Manual method error: {e}")
        return False

if __name__ == "__main__":
    print("Creating perfect resume with original formatting...")
    
    success = create_perfect_resume()
    
    if not success:
        print("\nTrying manual coordinate removal...")
        manual_coordinate_removal()
    
    print("\n✅ Done! Check the output files.")
    print("The new PDF should maintain the exact original formatting while removing the unwanted section.")
