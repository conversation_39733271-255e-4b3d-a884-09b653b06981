import fitz

def remove_education_percentages():
    """
    Remove percentages (67.7% and 73.5%) from education section
    """
    input_pdf = "Resume_Abhishek_IimL_NO_SPACE.pdf"
    output_pdf = "Resume_Abhishek_IimL_FINAL_CLEAN.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find and remove the education percentages
            percentages = ["67.7%", "73.5%"]
            
            for percentage in percentages:
                instances = page.search_for(percentage)
                
                for rect in instances:
                    print(f"Found '{percentage}' at: {rect}")
                    
                    # Get context to confirm it's in education section
                    context_rect = fitz.Rect(rect.x0 - 100, rect.y0 - 20, rect.x1 + 50, rect.y1 + 20)
                    context = page.get_textbox(context_rect)
                    print(f"Context: {context[:100]}...")
                    
                    # White out the percentage
                    expanded_rect = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                    page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    
                    print(f"✅ Removed '{percentage}' from education section")
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Removed education percentages (67.7% and 73.5%)")
        print("✅ All other formatting preserved")
        
        # Verify the removal
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        if "67.7%" not in verify_text and "73.5%" not in verify_text:
            print("✅ VERIFIED: Both percentages successfully removed")
        else:
            remaining = []
            if "67.7%" in verify_text:
                remaining.append("67.7%")
            if "73.5%" in verify_text:
                remaining.append("73.5%")
            print(f"⚠️  Still found: {', '.join(remaining)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Removing education percentages from the resume...")
    success = remove_education_percentages()
    
    if success:
        print("\n🎉 PERCENTAGES REMOVED!")
        print("📄 Resume_Abhishek_IimL_FINAL_CLEAN.pdf")
        print("Your resume is now clean without education percentages.")
    else:
        print("\n❌ Failed to remove percentages")
