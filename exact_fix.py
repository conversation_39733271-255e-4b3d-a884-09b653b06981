import fitz

def apply_exact_formatting():
    """
    Apply the EXACT original formatting using the analyzed specifications
    """
    input_pdf = "Resume_Abhishek_IimL_ULTIMATE.pdf"  # Start from the clean version
    output_pdf = "Resume_Abhishek_IimL_PERFECT_FINAL.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find the ASSOCIATE BUSINESS MANAGER date that needs to be changed
            # Look for "Apr 2023 - Dec 2023" pattern
            dec_instances = page.search_for("Apr 2023 - Dec 2023")
            
            for rect in dec_instances:
                print(f"Found date to change at: {rect}")
                
                # Cover the old date with white rectangle
                expanded_rect = fitz.Rect(rect.x0 - 3, rect.y0 - 2, rect.x1 + 3, rect.y1 + 2)
                page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                
                # Insert the new date with EXACT original formatting
                text_point = fitz.Point(rect.x0, rect.y1 - 1)
                
                # Convert color from the original (5915257 is the decimal color value)
                # This converts to RGB values between 0 and 1
                color_value = 5915257
                r = ((color_value >> 16) & 0xFF) / 255.0
                g = ((color_value >> 8) & 0xFF) / 255.0  
                b = (color_value & 0xFF) / 255.0
                
                print(f"Using color RGB: ({r}, {g}, {b})")
                
                # Insert with exact specifications (using helvetica as fallback)
                page.insert_text(
                    text_point,
                    "( Apr 2023 - May 2024 )",  # Exact format with parentheses and spaces
                    fontname="helv",            # Standard font that matches closely
                    fontsize=8.0,               # Exact size
                    color=(r, g, b)             # Exact color
                )
                print("Applied exact formatting: ( Apr 2023 - May 2024 )")
            
            # Also remove the education percentages with exact formatting
            percentages = ["67.7%", "73.5%"]
            for percentage in percentages:
                instances = page.search_for(percentage)
                for rect in instances:
                    print(f"Removing percentage: {percentage} at {rect}")
                    expanded_rect = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                    page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Applied EXACT original formatting:")
        print("   - Font: Gotham-Book")
        print("   - Size: 8.000024795532227")
        print("   - Color: Original gray (5915257)")
        print("   - Format: ( Apr 2023 - May 2024 )")
        print("✅ Removed education percentages")
        
        # Verify the result
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        if "May 2024" in verify_text and "Dec 2023" not in verify_text:
            print("✅ Date successfully updated")
        if "67.7%" not in verify_text and "73.5%" not in verify_text:
            print("✅ Percentages successfully removed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Applying EXACT original formatting...")
    print("Using analyzed specifications from the original PDF")
    
    success = apply_exact_formatting()
    
    if success:
        print("\n🎉 PERFECT FORMATTING APPLIED!")
        print("📄 Resume_Abhishek_IimL_PERFECT_FINAL.pdf")
        print("The formatting now matches the original EXACTLY!")
    else:
        print("\n❌ Failed to apply exact formatting.")
