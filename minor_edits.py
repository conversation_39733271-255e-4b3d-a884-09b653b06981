import fitz

def make_minor_changes():
    """
    Make specific minor changes to the ultimate PDF:
    1. Remove percentages (67.7% and 73.5%) from education section
    2. Change "Dec 2023" to "May 2024" in ASSOCIATE BUSINESS MANAGER section
    """
    input_pdf = "Resume_Abhishek_IimL_ULTIMATE.pdf"
    output_pdf = "Resume_Abhishek_IimL_FINAL.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find and remove the percentages from education section
            percentage_terms = ["67.7%", "73.5%"]
            
            for term in percentage_terms:
                instances = page.search_for(term)
                for rect in instances:
                    print(f"Found percentage '{term}' at {rect}")
                    # Cover with white rectangle
                    expanded_rect = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                    page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    print(f"Removed percentage: {term}")
            
            # Find and change "Dec 2023" to "May 2024"
            dec_instances = page.search_for("Dec 2023")
            for rect in dec_instances:
                print(f"Found 'Dec 2023' at {rect}")
                
                # First, cover the old text with white
                expanded_rect = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                
                # Then insert the new text "May 2024" at the same position
                # Calculate the position for the new text
                text_point = fitz.Point(rect.x0, rect.y1 - 2)
                
                # Insert new text with similar formatting
                page.insert_text(
                    text_point,
                    "May 2024",
                    fontsize=10,  # Approximate font size
                    color=(0, 0, 0)  # Black color
                )
                print("Changed 'Dec 2023' to 'May 2024'")
            
            # Also look for the pattern "Apr 2023 - Dec 2023" and change to "Apr 2023 - May 2024"
            full_date_instances = page.search_for("Apr 2023 - Dec 2023")
            for rect in full_date_instances:
                print(f"Found full date range 'Apr 2023 - Dec 2023' at {rect}")
                
                # Cover the old text
                expanded_rect = fitz.Rect(rect.x0 - 2, rect.y0 - 1, rect.x1 + 2, rect.y1 + 1)
                page.draw_rect(expanded_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                
                # Insert new text
                text_point = fitz.Point(rect.x0, rect.y1 - 2)
                page.insert_text(
                    text_point,
                    "Apr 2023 - May 2024",
                    fontsize=10,
                    color=(0, 0, 0)
                )
                print("Changed full date range to 'Apr 2023 - May 2024'")
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Removed education percentages (67.7% and 73.5%)")
        print("✅ Changed Dec 2023 to May 2024")
        
        # Verify the changes
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        # Check what was changed
        checks = [
            ("67.7%", "67.7%" not in verify_text, "Education percentage removed"),
            ("73.5%", "73.5%" not in verify_text, "Education percentage removed"),
            ("May 2024", "May 2024" in verify_text, "Date updated to May 2024"),
            ("Dec 2023", "Dec 2023" not in verify_text, "Old date removed")
        ]
        
        print("\n=== VERIFICATION ===")
        for item, success, description in checks:
            status = "✅" if success else "❌"
            print(f"{status} {description}: {item}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Making minor changes to the resume...")
    print("1. Removing education percentages (67.7% and 73.5%)")
    print("2. Changing Dec 2023 to May 2024 in ASSOCIATE BUSINESS MANAGER section")
    
    success = make_minor_changes()
    
    if success:
        print("\n🎉 MINOR CHANGES COMPLETED!")
        print("📄 Resume_Abhishek_IimL_FINAL.pdf")
        print("Your resume is now perfect and ready to use!")
    else:
        print("\n❌ Failed to make changes. Check error messages above.")
