import fitz

def aggressive_percentage_removal():
    """
    Aggressive removal by covering larger areas around percentages
    """
    input_pdf = "Resume_Abhishek_IimL_NO_SPACE.pdf"
    output_pdf = "Resume_Abhishek_IimL_CLEAN_FINAL.pdf"
    
    try:
        doc = fitz.open(input_pdf)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Define specific areas to white out based on the coordinates we found
            # These are the exact areas where the percentages appear
            percentage_areas = [
                # Area for 67.7% (BTECH section)
                fitz.Rect(30, 340, 70, 356),
                # Area for 73.5% (MBA section) 
                fitz.Rect(30, 387, 70, 403)
            ]
            
            for i, rect in enumerate(percentage_areas):
                print(f"Covering percentage area {i+1}: {rect}")
                
                # Draw a white rectangle over the entire area
                page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                
                print(f"✅ Covered area {i+1}")
        
        # Save the result
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ SUCCESS: Created {output_pdf}")
        print("✅ Aggressively covered percentage areas")
        
        # Final verification
        verify_doc = fitz.open(output_pdf)
        verify_text = ""
        for page in verify_doc:
            verify_text += page.get_text()
        verify_doc.close()
        
        print("\n=== FINAL VERIFICATION ===")
        checks = ["67.7%", "73.5%"]
        
        for check in checks:
            if check in verify_text:
                print(f"❌ Still visible: {check}")
            else:
                print(f"✅ Successfully removed: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Aggressive removal of education percentages...")
    aggressive_percentage_removal()
